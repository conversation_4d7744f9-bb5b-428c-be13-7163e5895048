["tests/test_advanced_features.py::TestConversationContext::test_add_messages", "tests/test_advanced_features.py::TestConversationContext::test_context_stats", "tests/test_advanced_features.py::TestConversationContext::test_context_summary", "tests/test_advanced_features.py::TestConversationContext::test_conversation_turns", "tests/test_advanced_features.py::TestConversationContext::test_initialization", "tests/test_advanced_features.py::TestConversationContext::test_session_save_load", "tests/test_advanced_features.py::TestFileTool::test_copy_move_operations", "tests/test_advanced_features.py::TestFileTool::test_directory_operations", "tests/test_advanced_features.py::TestFileTool::test_error_handling", "tests/test_advanced_features.py::TestFileTool::test_file_operations", "tests/test_advanced_features.py::TestFileTool::test_path_validation", "tests/test_advanced_features.py::TestFileTool::test_tool_properties", "tests/test_advanced_features.py::TestProfileManager::test_builtin_profiles_loaded", "tests/test_advanced_features.py::TestProfileManager::test_create_custom_profile", "tests/test_advanced_features.py::TestProfileManager::test_find_profiles_by_tag", "tests/test_advanced_features.py::TestProfileManager::test_get_profile", "tests/test_advanced_features.py::TestProfileManager::test_profile_recommendations", "tests/test_config.py::TestGlobalSettings::test_dotenv_loading", "tests/test_config.py::TestGlobalSettings::test_get_settings_singleton", "tests/test_config.py::TestGlobalSettings::test_reset_settings", "tests/test_config.py::TestSettings::test_config_directory_creation", "tests/test_config.py::TestSettings::test_default_settings", "tests/test_config.py::TestSettings::test_environment_variable_loading", "tests/test_config.py::TestSettings::test_get_api_key", "tests/test_config.py::TestSettings::test_to_dict", "tests/test_config.py::TestSettings::test_unsupported_provider", "tests/test_config.py::TestSettings::test_validation_errors", "tests/test_config.py::TestSettings::test_validation_success", "tests/test_tools.py::TestBashTool::test_command_failure", "tests/test_tools.py::TestBashTool::test_command_safety_analysis", "tests/test_tools.py::TestBashTool::test_dangerous_command_detection", "tests/test_tools.py::TestBashTool::test_parameter_validation", "tests/test_tools.py::TestBashTool::test_parameter_validation_error", "tests/test_tools.py::TestBashTool::test_successful_execution", "tests/test_tools.py::TestBashTool::test_system_modifying_detection", "tests/test_tools.py::TestBashTool::test_timeout_handling", "tests/test_tools.py::TestBashTool::test_tool_properties", "tests/test_tools.py::TestWebSearchTool::test_api_error_handling", "tests/test_tools.py::TestWebSearchTool::test_cache_functionality", "tests/test_tools.py::TestWebSearchTool::test_cache_key_generation", "tests/test_tools.py::TestWebSearchTool::test_cache_validity", "tests/test_tools.py::TestWebSearchTool::test_empty_query_handling", "tests/test_tools.py::TestWebSearchTool::test_parameter_validation", "tests/test_tools.py::TestWebSearchTool::test_parameter_validation_error", "tests/test_tools.py::TestWebSearchTool::test_result_formatting", "tests/test_tools.py::TestWebSearchTool::test_successful_search", "tests/test_tools.py::TestWebSearchTool::test_tool_properties"]