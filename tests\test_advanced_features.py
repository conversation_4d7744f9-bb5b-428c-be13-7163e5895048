"""
Tests for advanced features of Arien AI.
"""

import asyncio
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

import pytest

from src.config.profiles import Profile<PERSON>anager, ConfigProfile
from src.core.context import ConversationContext
from src.tools.file_tool import FileTool
from src.tools.base import ToolStatus


@pytest.mark.unit
class TestProfileManager:
    """Test ProfileManager functionality."""
    
    def test_builtin_profiles_loaded(self):
        """Test that built-in profiles are loaded."""
        manager = ProfileManager()
        profiles = manager.list_profiles()
        
        expected_profiles = [
            "development", "production", "research", "sysadmin",
            "learning", "security", "performance", "minimal"
        ]
        
        for profile_name in expected_profiles:
            assert profile_name in profiles
            assert isinstance(profiles[profile_name], ConfigProfile)
    
    def test_get_profile(self):
        """Test getting a specific profile."""
        manager = ProfileManager()
        
        # Test existing profile
        dev_profile = manager.get_profile("development")
        assert dev_profile is not None
        assert dev_profile.name == "development"
        assert "dev" in dev_profile.tags
        
        # Test non-existing profile
        missing_profile = manager.get_profile("nonexistent")
        assert missing_profile is None
    
    def test_find_profiles_by_tag(self):
        """Test finding profiles by tag."""
        manager = ProfileManager()
        
        dev_profiles = manager.find_profiles_by_tag("dev")
        assert len(dev_profiles) > 0
        assert all("dev" in profile.tags for profile in dev_profiles)
        
        security_profiles = manager.find_profiles_by_tag("security")
        assert len(security_profiles) > 0
        assert all("security" in profile.tags for profile in security_profiles)
    
    def test_create_custom_profile(self):
        """Test creating custom profiles."""
        manager = ProfileManager()
        
        custom_profile = manager.create_custom_profile(
            name="test_custom",
            description="Test custom profile",
            base_profile="development",
            overrides={"llm.temperature": 0.5},
            tags=["test", "custom"]
        )
        
        assert custom_profile.name == "test_custom"
        assert custom_profile.description == "Test custom profile"
        assert "test" in custom_profile.tags
        assert custom_profile.settings.llm.temperature == 0.5
        
        # Verify it's stored
        retrieved = manager.get_profile("test_custom")
        assert retrieved is not None
        assert retrieved.name == "test_custom"
    
    def test_profile_recommendations(self):
        """Test profile recommendations."""
        manager = ProfileManager()
        
        # Test development use case
        dev_recs = manager.get_profile_recommendations("I want to develop Python applications")
        assert len(dev_recs) > 0
        assert any(profile.name == "development" for profile in dev_recs)
        
        # Test research use case
        research_recs = manager.get_profile_recommendations("I need to research AI trends")
        assert len(research_recs) > 0
        assert any(profile.name == "research" for profile in research_recs)
        
        # Test unknown use case (should default to production)
        unknown_recs = manager.get_profile_recommendations("something completely unknown")
        assert len(unknown_recs) > 0
        assert any(profile.name == "production" for profile in unknown_recs)


@pytest.mark.unit
class TestConversationContext:
    """Test ConversationContext functionality."""
    
    def test_initialization(self):
        """Test context initialization."""
        context = ConversationContext()
        
        assert context.session_info is not None
        assert context.session_info.session_id is not None
        assert len(context.messages) == 0
        assert len(context.conversation_turns) == 0
    
    def test_add_messages(self):
        """Test adding different types of messages."""
        context = ConversationContext()
        
        # Add system message
        context.add_system_message("You are a helpful assistant.")
        assert len(context.messages) == 1
        assert context.messages[0].role.value == "system"
        
        # Add user message
        context.add_user_message("Hello!")
        assert len(context.messages) == 2
        assert context.messages[1].role.value == "user"
        
        # Add assistant message
        context.add_assistant_message("Hi there!")
        assert len(context.messages) == 3
        assert context.messages[2].role.value == "assistant"
    
    def test_conversation_turns(self):
        """Test conversation turn management."""
        context = ConversationContext()
        
        # Add a conversation turn
        context.add_conversation_turn(
            user_input="Test input",
            ai_response="Test response",
            tool_executions=[{
                "tool_name": "test_tool",
                "parameters": {"param": "value"},
                "result": {"status": "success", "output": "test output"}
            }]
        )
        
        assert len(context.conversation_turns) == 1
        turn = context.conversation_turns[0]
        assert turn.user_input == "Test input"
        assert turn.ai_response == "Test response"
        assert len(turn.tool_executions) == 1
    
    def test_context_stats(self):
        """Test context statistics."""
        context = ConversationContext()
        
        context.add_system_message("System message")
        context.add_user_message("User message")
        context.add_conversation_turn(
            user_input="Test",
            ai_response="Response",
            tool_executions=[{"tool_name": "bash", "parameters": {}, "result": {}}]
        )
        
        stats = context.get_context_stats()
        
        assert stats["total_messages"] == 2
        assert stats["total_turns"] == 1
        assert stats["tools_used"] == 1
        assert "session_duration" in stats
        assert "estimated_tokens" in stats
    
    def test_context_summary(self):
        """Test context summary generation."""
        context = ConversationContext()
        
        # Empty context
        summary = context.generate_context_summary()
        assert "No conversation history" in summary
        
        # Add some conversation
        context.add_conversation_turn(
            user_input="Help with Python programming",
            ai_response="I can help with Python",
            tool_executions=[{"tool_name": "bash", "parameters": {}, "result": {}}]
        )
        
        summary = context.generate_context_summary()
        assert "1 turns" in summary
        assert "bash" in summary
    
    def test_session_save_load(self):
        """Test session save and load functionality."""
        context = ConversationContext()

        # Add some data
        context.add_user_message("Test message")
        context.add_conversation_turn(
            user_input="Test input",
            ai_response="Test response",
            tool_executions=[]
        )

        # Save to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file_path = f.name

        try:
            context.save_session(temp_file_path)

            # Load into new context
            new_context = ConversationContext()
            new_context.load_session(temp_file_path)

            # Verify data was loaded
            assert len(new_context.conversation_turns) == 1
            assert new_context.conversation_turns[0].user_input == "Test input"

        finally:
            # Cleanup
            try:
                Path(temp_file_path).unlink()
            except (FileNotFoundError, PermissionError):
                pass  # File might already be deleted or locked


@pytest.mark.unit
class TestFileTool:
    """Test FileTool functionality."""
    
    @pytest.mark.asyncio
    async def test_file_operations(self):
        """Test basic file operations."""
        tool = FileTool()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            test_file = temp_path / "test.txt"
            
            # Test write operation
            result = await tool.execute(
                operation="write",
                path=str(test_file),
                content="Hello, World!"
            )
            assert result.status == ToolStatus.SUCCESS
            assert test_file.exists()
            
            # Test read operation
            result = await tool.execute(
                operation="read",
                path=str(test_file)
            )
            assert result.status == ToolStatus.SUCCESS
            assert "Hello, World!" in result.output
            
            # Test file info
            result = await tool.execute(
                operation="info",
                path=str(test_file)
            )
            assert result.status == ToolStatus.SUCCESS
            assert "file" in result.output.lower()
            
            # Test file exists
            result = await tool.execute(
                operation="exists",
                path=str(test_file)
            )
            assert result.status == ToolStatus.SUCCESS
            assert "exists" in result.output
    
    @pytest.mark.asyncio
    async def test_directory_operations(self):
        """Test directory operations."""
        tool = FileTool()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            new_dir = temp_path / "new_directory"
            
            # Test mkdir operation
            result = await tool.execute(
                operation="mkdir",
                path=str(new_dir)
            )
            assert result.status == ToolStatus.SUCCESS
            assert new_dir.exists()
            assert new_dir.is_dir()
            
            # Create a test file in the directory
            test_file = new_dir / "test.txt"
            test_file.write_text("test content")
            
            # Test list operation
            result = await tool.execute(
                operation="list",
                path=str(new_dir)
            )
            assert result.status == ToolStatus.SUCCESS
            assert "test.txt" in result.output
    
    @pytest.mark.asyncio
    async def test_copy_move_operations(self):
        """Test copy and move operations."""
        tool = FileTool()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            source_file = temp_path / "source.txt"
            dest_file = temp_path / "destination.txt"
            
            # Create source file
            source_file.write_text("source content")
            
            # Test copy operation
            result = await tool.execute(
                operation="copy",
                source=str(source_file),
                destination=str(dest_file)
            )
            assert result.status == ToolStatus.SUCCESS
            assert dest_file.exists()
            assert dest_file.read_text() == "source content"
            assert source_file.exists()  # Original should still exist
            
            # Test move operation
            moved_file = temp_path / "moved.txt"
            result = await tool.execute(
                operation="move",
                source=str(dest_file),
                destination=str(moved_file)
            )
            assert result.status == ToolStatus.SUCCESS
            assert moved_file.exists()
            assert not dest_file.exists()  # Original should be gone
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test file tool error handling."""
        tool = FileTool()

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            nonexistent_file = temp_path / "nonexistent" / "file.txt"

            # Test reading non-existent file
            result = await tool.execute(
                operation="read",
                path=str(nonexistent_file)
            )
            assert result.status == ToolStatus.ERROR
            assert "not found" in result.error.lower()

            # Test invalid operation
            result = await tool.execute(
                operation="invalid_operation",
                path=str(temp_path / "test.txt")
            )
            assert result.status == ToolStatus.ERROR
            assert "unknown operation" in result.error.lower()

            # Test missing required parameters
            result = await tool.execute(
                operation="read"
                # Missing path parameter
            )
            assert result.status == ToolStatus.ERROR
            assert ("validation failed" in result.error.lower() or
                    "path" in result.error.lower())
    
    @pytest.mark.asyncio
    async def test_path_validation(self):
        """Test path validation and security."""
        tool = FileTool()

        # Test path traversal protection
        result = await tool.execute(
            operation="read",
            path="../../../etc/passwd"
        )
        assert result.status == ToolStatus.ERROR
        assert "traversal" in result.error.lower()

        # Test protected path access
        result = await tool.execute(
            operation="read",
            path="C:\\"
        )
        assert result.status == ToolStatus.ERROR
        assert "protected path" in result.error.lower()
    
    def test_tool_properties(self):
        """Test tool properties."""
        tool = FileTool()
        
        assert tool.name == "file"
        assert isinstance(tool.description, str)
        assert len(tool.description) > 0
        assert tool.can_run_parallel is False  # File operations should be sequential
        assert tool.is_destructive is True  # File operations can be destructive
        
        # Test parameter schema
        schema = tool.get_schema()
        assert schema["type"] == "function"
        assert "operation" in schema["function"]["parameters"]["properties"]
        assert "path" in schema["function"]["parameters"]["properties"]
