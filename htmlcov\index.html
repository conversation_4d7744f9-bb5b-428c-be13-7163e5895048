<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">44%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-08 12:26 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html">src\config\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html">src\config\profiles.py</a></td>
                <td>142</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="116 142">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html">src\config\settings.py</a></td>
                <td>106</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="101 106">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html">src\core\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html">src\core\agent.py</a></td>
                <td>147</td>
                <td>108</td>
                <td>0</td>
                <td class="right" data-ratio="39 147">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html">src\core\context.py</a></td>
                <td>142</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="90 142">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html">src\core\exceptions.py</a></td>
                <td>62</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="50 62">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html">src\core\logging.py</a></td>
                <td>92</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="0 92">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html">src\core\retry.py</a></td>
                <td>84</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="54 84">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html">src\main.py</a></td>
                <td>199</td>
                <td>199</td>
                <td>0</td>
                <td class="right" data-ratio="0 199">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60___init___py.html">src\providers\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html">src\providers\base.py</a></td>
                <td>78</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="48 78">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html">src\providers\deepseek.py</a></td>
                <td>105</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="23 105">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html">src\providers\ollama.py</a></td>
                <td>115</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="24 115">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40___init___py.html">src\tools\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html">src\tools\base.py</a></td>
                <td>71</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="57 71">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html">src\tools\bash_tool.py</a></td>
                <td>98</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="91 98">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html">src\tools\file_tool.py</a></td>
                <td>249</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="150 249">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html">src\tools\web_search_tool.py</a></td>
                <td>136</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="122 136">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9___init___py.html">src\ui\__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html">src\ui\animation.py</a></td>
                <td>115</td>
                <td>115</td>
                <td>0</td>
                <td class="right" data-ratio="0 115">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html">src\ui\cli.py</a></td>
                <td>145</td>
                <td>145</td>
                <td>0</td>
                <td class="right" data-ratio="0 145">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html">src\ui\formatter.py</a></td>
                <td>133</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="0 133">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>2242</td>
                <td>1258</td>
                <td>0</td>
                <td class="right" data-ratio="984 2242">44%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-08 12:26 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_d8dae034b85f0cf9_formatter_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_145eef247bfb46b6___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
